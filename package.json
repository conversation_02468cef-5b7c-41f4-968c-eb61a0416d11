{"name": "zinia-portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@types/lodash": "^4.17.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cobe": "^0.6.4", "lenis": "^1.3.4", "lodash": "^4.17.21", "lucide-react": "^0.525.0", "motion": "^12.23.3", "next": "15.3.5", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@types/node": "^24.0.13", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "eslint": "^9.31.0", "eslint-config-next": "15.3.5", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "^5.8.3"}}