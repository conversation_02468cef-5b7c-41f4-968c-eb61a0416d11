import Earth from "@/components/cool/globe";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { instrumentserif } from "@/lib/font";
import { cn } from "@/lib/utils";
import { motion, useInView } from "motion/react";
import { useRef } from "react";

export default function ContactUs1() {
  const formRef = useRef(null);
  const isInView = useInView(formRef, { once: true, amount: 0.3 });

  if (typeof window === "undefined") {
    return null;
  }

  return (
    <section
      className="relative w-full overflow-hidden bg-background py-16 md:py-24"
      id="contact"
    >
      <div
        className="absolute left-0 top-0 h-[500px] w-[500px] rounded-full opacity-20 blur-[120px]"
        style={{
          background: `radial-gradient(circle at center, #e60a64, transparent 70%)`,
        }}
      />
      <div
        className="absolute bottom-0 right-0 h-72 w-72 rounded-full opacity-10 blur-[100px]"
        style={{
          background: `radial-gradient(circle at center, #e60a64, transparent 70%)`,
        }}
      />

      <div className="container relative z-10 mx-auto px-4 md:px-6">
        <div className="mx-auto max-w-5xl overflow-hidden rounded-4xl border border-border/40 bg-secondary/20 shadow backdrop-blur-sm">
          <div className="grid md:grid-cols-2">
            <div className="relative p-6 md:p-10" ref={formRef}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={
                  isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }
                }
                transition={{ duration: 0.5, delay: 0.1 }}
                className="flex w-full gap-2"
              >
                <h2 className="mb-2 bg-gradient-to-r from-foreground to-foreground/60 bg-clip-text text-4xl tracking-tighter text-transparent md:text-5xl">
                  Contact
                </h2>
                <span className="text-4xl text-primary md:text-5xl">Me</span>
              </motion.div>

              <motion.form
                initial={{ opacity: 0, y: 20 }}
                animate={
                  isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }
                }
                transition={{ duration: 0.5, delay: 0.3 }}
                action="https://formsubmit.co/<EMAIL>"
                method="POST"
                className="mt-8 space-y-6"
              >
                <input
                  type="hidden"
                  name="_cc"
                  value="<EMAIL>"
                />
                <input type="hidden" name="_captcha" value="false" />
                <input type="hidden" name="_template" value="box" />
                <input
                  type="hidden"
                  name="_next"
                  value={window.location.origin + "/thanku"}
                />
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="name">Name</Label>
                    <Input
                      id="name"
                      name="name"
                      placeholder="Enter your name"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      name="email"
                      placeholder="Enter your email"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="message">Message</Label>
                  <Textarea
                    id="message"
                    name="message"
                    placeholder="Enter your message"
                    required
                    className="h-40 resize-none"
                  />
                </div>

                <Button
                  type="submit"
                  className="shadow-[0px_2px_0px_0px_rgba(255,255,255,0.3)_inset]"
                >
                  <span>Send Message</span>
                </Button>
              </motion.form>
            </div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 20 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              className="relative mt-6 sm:my-8 flex items-center justify-center overflow-hidden"
            >
              <div className="flex flex-col items-center justify-center overflow-hidden">
                <article
                  className={cn(
                    "relative mx-auto h-96 min-h-60 max-w-[450px] overflow-hidden rounded-4xl border bg-gradient-to-b via-primary/30 from-primary p-6 text-3xl tracking-tighter txt-glow text-white md:h-[450px] md:p-8 md:text-4xl lg:text-5xl italic leading-snug",
                    instrumentserif.className
                  )}
                >
                  I would love to bring your ideas to life!
                  <div className="absolute -bottom-20 -right-20 z-10 mx-auto flex h-full w-full max-w-[300px] items-center justify-center transition-all duration-700 hover:scale-105 md:-bottom-28 md:-right-28 md:max-w-[550px] brightness-[6] dark:brightness-100">
                    <Earth
                      scale={1.1}
                      baseColor={[1, 0.1, 0.1]}
                      markerColor={[0, 0, 0]}
                      glowColor={[1, 0.3, 0.4]}
                    />
                  </div>
                </article>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}
