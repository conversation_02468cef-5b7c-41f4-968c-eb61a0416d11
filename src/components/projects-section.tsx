"use client";

import { motion } from "motion/react";
import Image, { StaticImageData } from "next/image";
import { BlurFade } from "./cool/blur-fade";
import { cn } from "@/lib/utils";
import { instrumentserif } from "@/lib/font";
import { ProgressiveBlur } from "./cool/progressive-blur";
import { projects } from "@/lib/data";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { useState } from "react";
import { Button } from "./ui/button";
import { Download } from "lucide-react";

export default function ProjectsSection() {
  const [open, setOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState<StaticImageData | null>(
    null
  );
  const [selectedTitle, setSelectedTitle] = useState<string | null>(null);

  return (
    <>
      <section id="projects" className="py-20 md:py-28">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-5xl md:text-6xl mb-6 tracking-tighter text-accent-foreground/80">
              Featured{" "}
              <span
                className={cn(
                  "text-primary txt-glow-primary italic opacity-100",
                  instrumentserif.className
                )}
              >
                Projects
              </span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto font-light tracking-tight">
              A showcase of my recent work and creative solutions
            </p>
          </motion.div>

          <div className="columns-1 md:columns-2 lg:columns-3 gap-6 space-y-10">
            {projects.map((project, idx) => (
              <BlurFade
                key={idx}
                delay={0.25 + idx * 0.1}
                inView
                direction="up"
              >
                <div
                  onClick={() => {
                    setSelectedImage(project.image);
                    setSelectedTitle(project.title);
                    setOpen(true);
                  }}
                  className="w-full h-auto shadow-xs rounded-4xl hover:shadow-2xl transition-all duration-500 cursor-pointer overflow-hidden relative group"
                >
                  <Image
                    className="object-contain hover:scale-[1.02] transition-all duration-500 w-full"
                    src={project.image}
                    alt={project.title}
                    width={project.image.width}
                    height={400}
                    placeholder="blur"
                    fetchPriority="low"
                  />
                  <ProgressiveBlur
                    className="pointer-events-none absolute bottom-0 left-0 h-24 w-full group-hover:opacity-100 opacity-0 transition-all"
                    blurIntensity={2}
                  />
                  <div className="absolute w-full bottom-0 left-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent group-hover:opacity-100 opacity-0 transition-all duration-500">
                    <div className="flex flex-col items-start gap-2 px-6 py-4">
                      <p className="text-base font-medium text-white txt-glow px-2">
                        {project.title}
                      </p>
                      <p className="text-base text-white">
                        {project.tags.map((tag, idx) => (
                          <span
                            key={idx}
                            className="inline-block bg-white/10 rounded-full px-3 py-1 text-sm font-semibold mr-2 cursor-pointer hover:scale-105 transition-all duration-300"
                          >
                            {tag}
                          </span>
                        ))}
                      </p>
                    </div>
                  </div>
                </div>
              </BlurFade>
            ))}
          </div>
        </div>
      </section>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="max-w-3xl p-1 sm:p-3 bg-white/10 backdrop-blur-xl border-border/20 group">
          {selectedImage && (
            <div className="relative">
              <Image
                src={selectedImage}
                alt={selectedTitle || "Project image"}
                width={1000}
                fetchPriority="low"
                placeholder="blur"
                height={800}
                className="object-contain w-full max-h-screen rounded-lg"
              />
              <a href={selectedImage.src} download title="Download Image">
                <Button
                  className="absolute bottom-2 right-2 text-xs sm:text-sm bg-background/30 backdrop-blur-md shadow-2xl border-border/20 hover:bg-background/50 transition-all duration-300 sm:group-hover:opacity-100 sm:group-active:opacity-100 sm:opacity-0"
                  size={"sm"}
                  variant={"outline"}
                >
                  <Download size={18} />
                </Button>
              </a>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
