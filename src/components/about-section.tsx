import { motion } from "motion/react";
import { ArrowRight } from "lucide-react";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { instrumentserif } from "@/lib/font";
import { Button } from "./ui/button";
import { BlurVignette } from "./cool/blur-vignette";
import Link from "next/link";

export default function AboutSection() {
  return (
    <section id="about" className="py-20 max-w-screen overflow-x-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, ease: [0.25, 0.1, 0.25, 1] }}
            viewport={{ amount: 0.3 }}
          >
            {/* Badge */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{
                duration: 0.6,
                delay: 0.1,
                ease: [0.25, 0.1, 0.25, 1],
              }}
              viewport={{ once: true, amount: 0.3 }}
              className="inline-flex items-center gap-2 bg-cyan-500/10 text-cyan-500 px-4 py-2 rounded-full text-sm font-medium mb-8 shadow-xs"
            >
              <div className="w-2 h-2 bg-cyan-500 rounded-full animate-pulse"></div>
              Available for work
            </motion.div>

            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{
                duration: 0.8,
                delay: 0.15,
                ease: [0.25, 0.1, 0.25, 1],
              }}
              viewport={{ once: true, amount: 0.3 }}
              className={cn(
                "text-5xl md:text-6xl lg:text-7xl italic tracking-tighter mb-8 leading-tight txt-glow",
                instrumentserif.className
              )}
            >
              About Me
            </motion.h2>

            <motion.p
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{
                duration: 0.6,
                delay: 0.2,
                ease: [0.25, 0.1, 0.25, 1],
              }}
              viewport={{ once: true, amount: 0.3 }}
              className="text-lg text-muted-foreground mb-10 max-w-lg"
            >
              Lorem ipsum, dolor sit amet consectetur adipisicing elit. Magni
              hic, inventore ipsum qui laudantium provident deserunt fuga magnam
              molestiae dignissimos modi totam? Non suscipit iusto provident
              dolorum soluta deleniti accusantium.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{
                duration: 1.2,
                delay: 0.25,
                ease: [0.25, 0.1, 0.25, 1],
              }}
              viewport={{ once: true, amount: 0.3 }}
              className="flex flex-col sm:flex-row gap-4"
            >
              <a href="#projects">
                <Button className="group">
                  My Projects
                  <ArrowRight
                    size={20}
                    className="ml-2 group-hover:ml-3 group-hover:rotate-45 transition-all duration-300"
                  />
                </Button>
              </a>
              <a
                href="http://anish7.me"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Button variant={"secondary"}>My CV</Button>
              </a>
            </motion.div>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, x: 50, scale: 0.9, filter: "blur(8px)" }}
            whileInView={{ opacity: 1, x: 0, scale: 1, filter: "blur(0px)" }}
            transition={{ duration: 1, delay: 0.3, ease: [0.25, 0.1, 0.25, 1] }}
            viewport={{ once: true, amount: 0.3 }}
            className="relative w-full max-w-lg mx-auto"
          >
            <BlurVignette
              radius="24px"
              inset="20px"
              transitionLength="120px"
              blur="40px"
              className="flex-1 rounded-t-full overflow-hidden shadow"
            >
              <Image
                width={500}
                height={500}
                className="size-full object-cover"
                src="https://assets.lummi.ai/assets/Qme5SbrfweyVJASb23XmxwKUZCWLur1KGTCKe3TmNGRjj5?auto=format&w=1000"
                fetchPriority="low"
                alt="Zinia"
              />
            </BlurVignette>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
