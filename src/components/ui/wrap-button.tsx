import React from "react";
import Link from "next/link";
import { ArrowRight } from "lucide-react";

import { cn } from "@/lib/utils";

interface WrapButtonProps {
  className?: string;
  children: React.ReactNode;
  href: string;
}

const WrapButton: React.FC<WrapButtonProps> = ({
  className,
  children,
  href,
}) => {
  return (
    <div className="flex items-center justify-center">
      <Link href={href}>
        <div
          className={cn(
            "group cursor-pointer border group bg-background gap-2 flex items-center p-2 rounded-full shadow-lg active:scale-95 transition-all duration-300",
            className
          )}
        >
          <div className="border border-border bg-primary p-3 rounded-full flex items-center justify-center text-primary-foreground">
            <p className="font-medium tracking-tight mr-3 ml-2 flex items-center gap-2 justify-center ">
              {children}
            </p>
          </div>
          <div className="text-primary group-hover:ml-2 ease-in-out transition-all duration-300 size-10 flex items-center justify-center rounded-full border-2 border-primary">
            <ArrowRight
              size={18}
              className="group-hover:rotate-45 ease-in-out transition-all duration-500"
            />
          </div>
        </div>
      </Link>
    </div>
  );
};

export default WrapButton;
