import { motion } from "motion/react";
import { Globe } from "lucide-react";
import Image from "next/image";
import WrapButton from "./ui/wrap-button";
import { cn } from "@/lib/utils";
import { instrumentserif } from "@/lib/font";
import { Tilt } from "./cool/tilt";
import a1 from "@/assets/avatar1.png";
import a2 from "@/assets/avatar2.png";
import a3 from "@/assets/avatar3.png";

const shapes = [
  {
    id: 1,
    color: "bg-[#FC0A56]",
    size: "w-20 h-20",
    position: "top-20 left-10",
  },
  {
    id: 2,
    color: "bg-blue-500",
    size: "w-16 h-16",
    position: "top-40 right-20",
  },
  {
    id: 3,
    color: "bg-yellow-400",
    size: "w-12 h-12",
    position: "bottom-40 left-20",
  },
  {
    id: 4,
    color: "bg-green-400",
    size: "w-24 h-24",
    position: "bottom-20 right-10",
  },
  {
    id: 5,
    color: "bg-purple-500",
    size: "w-14 h-14",
    position: "top-60 left-1/3",
  },
  {
    id: 6,
    color: "bg-cyan-400",
    size: "w-18 h-18",
    position: "bottom-60 right-1/3",
  },
];
export default function HeroSection() {
  return (
    <section
      id="home"
      className="relative min-h-screen flex items-center justify-center pt-20 overflow-hidden bg-background"
    >
      <motion.div
        className="absolute inset-0 z-0 select-none pointer-events-none opacity-20 dark:opacity-35"
        style={{
          background:
            "radial-gradient(125% 135% at 50% 90%, transparent 40%, var(--primary) 130%)",
        }}
      />
      {/* {shapes.map((shape) => (
        <motion.div
          key={shape.id}
          className={`absolute ${shape.size} ${shape.color} ${shape.position} rounded-2xl opacity-80`}
          initial={{ scale: 0, rotate: 0 }}
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
            y: [0, -20, 0],
          }}
          transition={{
            duration: 6,
            repeat: Number.POSITIVE_INFINITY,
            delay: shape.id * 0.5,
            ease: [0.25, 0.1, 0.25, 1],
          }}
          style={{
            background: `linear-gradient(45deg, ${shape.color.replace("bg-", "")}, transparent)`,
          }}
        />
      ))} */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 0.3 }}
        transition={{ duration: 1, ease: [0.25, 0.1, 0.25, 1] }}
        className="absolute inset-0 flex items-center justify-center pointer-events-none overflow-hidden"
      >
        <div className="overflow-hidden space-y-5 masked">
          <div className="text-big flex gap-8 whitespace-nowrap tracking-tighter font-bold w-max pl-8 marquee-text ">
            <p>ZINIA</p>
            <p>KHATUN</p>
            <p> ✦ </p>
            <p>ZINIA</p>
            <p>KHATUN</p>
            <p> ✦ </p>
          </div>
        </div>
      </motion.div>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: [0.25, 0.1, 0.25, 1] }}
          className="relative z-20"
          viewport={{ once: true, margin: "-50px" }}
        >
          <motion.h2
            initial={{ opacity: 0, y: 30, filter: "blur(10px)" }}
            animate={{ opacity: 1, y: 0, filter: "blur(0px)" }}
            transition={{
              delay: 0.4,
              duration: 1,
              ease: [0.25, 0.1, 0.25, 1],
            }}
            className="text-5xl mb-4 tracking-tighter wrap-break-word"
          >
            Hi, I&apos;m{" "}
            <span
              className={cn(
                instrumentserif.className,
                "text-primary italic text-6xl ml-1 tracking-[-4%] txt-glow-primary"
              )}
            >
              Zinia!
            </span>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 30, filter: "blur(10px)" }}
            animate={{ opacity: 1, y: 0, filter: "blur(0px)" }}
            transition={{
              delay: 0.6,
              duration: 1,
              ease: [0.25, 0.1, 0.25, 1],
            }}
            className="text-xl md:text-2xl text-muted-foreground mb-5"
          >
            UI/UX Designer
          </motion.p>

          <Tilt isRevese>
            <motion.div
              initial={{ scale: 0, filter: "blur(20px)" }}
              animate={{ scale: 1, filter: "blur(0px)" }}
              transition={{
                delay: 0.8,
                duration: 2,
                type: "spring",
                ease: [0.25, 0.1, 0.25, 1],
              }}
              className="relative w-56 h-56 mx-auto mb-8 "
            >
              <div className="rounded-4xl bg-gradient-to-br from-primary to-purple-600 p-1 absolute inset-0 z-0 blur-xl opacity-50" />
              <div className="w-full h-full rounded-4xl bg-gradient-to-br from-primary to-purple-600 p-1 shadow-2xl hover:scale-110 transition-all duration-500 cursor-pointer hover:rotate-3 group z-20 relative">
                <Image
                  src="https://images.pexels.com/photos/27765567/pexels-photo-27765567.jpeg"
                  alt="Zinia Khatun"
                  width={250}
                  height={250}
                  priority
                  // placeholder="blur"
                  className="w-full h-full rounded-4xl object-cover"
                />
              </div>
            </motion.div>
          </Tilt>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
              delay: 0.5,
              duration: 0.8,
              ease: [0.25, 0.1, 0.25, 1],
            }}
            className="flex items-center justify-center gap-2 mb-8"
          >
            <div className="flex -space-x-2">
              {[a1, a2, a3].map((url, index) => (
                <Image
                  key={index}
                  alt={"client" + index}
                  src={url}
                  priority
                  width={36}
                  height={36}
                  className={
                    "size-8 rounded-full border-2 border-border/30 shadow cursor-pointer hover:-translate-y-1 transition-all duration-300 hover:scale-110 z-" +
                    index +
                    "0"
                  }
                />
              ))}
            </div>
            <span className="text-muted-foreground ml-1">
              30+ Happy Clients
            </span>
          </motion.div>

          <WrapButton className="mt-4" href="#contact">
            <Globe className="animate-spin" />
            Hire me
          </WrapButton>
        </motion.div>
      </div>
    </section>
  );
}
