"use client";

import { motion } from "motion/react";
import { Star } from "lucide-react";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { instrumentserif } from "@/lib/font";
import { testimonials } from "@/lib/data";

export default function TestimonialsSection() {
  return (
    <section id="testimonials" className="py-20 md:py-32">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2
            className={cn(
              "text-5xl md:text-6xl font-thin mb-6 txt-glow tracking-tight",
              instrumentserif.className
            )}
          >
            Testimonials
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            What our clients have to say about us.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-8">
          {testimonials.map((testimonial) => (
            <div
              key={testimonial.id}
              className={cn(
                "bg-gradient-to-b from-card dark:to-accent rounded-2xl p-8 border border-border/50 hover:border-primary/30 hover:-translate-y-1 transition-all duration-300 cursor-pointer dark:shadowinset relative overflow-hidden shadow",
                instrumentserif.className
              )}
            >
              <div className="absolute bg-primary w-1/2 h-8 blur-3xl -bottom-4 right-1/2 translate-x-1/2  dark:opacity-60 opacity-20"></div>
              <div className="flex items-center mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star
                    key={i}
                    className="text-amber-400 fill-current"
                    size={22}
                  />
                ))}
              </div>

              <p className="mb-6 leading-relaxed tracking-wide italic">
                {testimonial.content}
              </p>

              <div className="flex items-center">
                <Image
                  src={testimonial.image}
                  alt={testimonial.name}
                  width={60}
                  height={60}
                  className="w-12 h-12 rounded-full object-cover mr-4"
                />
                <div>
                  <h4 className="text-foreground font-semibold">
                    {testimonial.name}
                  </h4>
                  <p className="text-muted-foreground text-sm">
                    {testimonial.role}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
