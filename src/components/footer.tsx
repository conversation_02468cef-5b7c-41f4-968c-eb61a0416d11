"use client";

import { instrumentserif } from "@/lib/font";
import { cn } from "@/lib/utils";
import { Github, Instagram, Linkedin } from "lucide-react";
import { motion } from "motion/react";

const socialLinks = [
  { icon: Github, href: "https://github.com/ziniakhatun", label: "GitHub" },
  {
    icon: Linkedin,
    href: "https://www.linkedin.com/in/zinia-khatun-23b6a8329/",
    label: "LinkedIn",
  },
  {
    icon: Instagram,
    href: "https://www.instagram.com/917198.0_/",
    label: "Instagram",
  },
];
export default function Footer() {
  return (
    <footer className="bg-card border-t rounded-4xl m-4 shadow-2xl overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid lg:grid-cols-2 gap-8 items-center">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, ease: [0.25, 0.1, 0.25, 1] }}
            viewport={{ once: true }}
          >
            <h3 className="text-4xl md:text-5xl font-light text-accent-foreground/80 mb-4 tracking-tighter">
              Can&apos;t wait to work <br />{" "}
              <span
                className={cn(instrumentserif.className, "italic txt-glow")}
              >
                together!
              </span>
            </h3>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 1, ease: [0.25, 0.1, 0.25, 1] }}
            viewport={{ once: true, amount: 0.3 }}
            className="text-right space-y-4"
          >
            <div>
              <p className="text-muted-foreground text-sm uppercase tracking-wider">
                Email
              </p>
              <a
                href="mailto:<EMAIL>"
                className="text-foreground text-xl font-medium hover:underline transition-all duration-500 hover:text-primary/80"
              >
                <EMAIL>
              </a>
            </div>
            <div>
              <p className="text-muted-foreground text-sm uppercase tracking-wider">
                Follow
              </p>
              <a
                href="https://www.instagram.com/917198.0_/"
                className="text-foreground text-xl font-medium hover:underline transition-all duration-500 hover:text-primary/80"
              >
                @917198.0_
              </a>
            </div>
            <div>
              <p className="text-muted-foreground text-sm uppercase tracking-wider">
                Call
              </p>
              <a
                href="tel:+919932545531"
                className="text-foreground text-xl font-medium hover:underline transition-all duration-500 hover:text-primary/80"
              >
                +91 9932545531
              </a>
            </div>
          </motion.div>
        </div>

        <div className="mt-12 pt-8 border-t border-border flex flex-col md:flex-row justify-between items-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, ease: [0.25, 0.1, 0.25, 1] }}
            viewport={{ once: true, amount: 0.3 }}
            className="flex items-center space-x-8 mb-4 md:mb-0"
          >
            <p className="text-muted-foreground text-sm">
              OPEN TO NEW PROJECTS
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, ease: [0.25, 0.1, 0.25, 1] }}
            viewport={{ once: true, amount: 0.3 }}
            className="flex items-center space-x-6"
          >
            <p className="text-muted-foreground text-sm tracking-tight">
              DESIGNED & DEVELOPED BY{" "}
              <a
                href="http://anish7.me"
                target="_blank"
                rel="noopener noreferrer"
                className="text-foreground hover:text-primary transition-all duration-300 hover:font-black hover:tracking-wider"
              >
                ANISH✨
              </a>
            </p>
            <p className="text-muted-foreground text-sm">©2025</p>
          </motion.div>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: [0.25, 0.1, 0.25, 1] }}
          viewport={{ once: true, amount: 0.3 }}
          className="mt-8 flex justify-center space-x-6"
        >
          {socialLinks.map((social) => (
            <a
              key={social.label}
              href={social.href}
              target="_blank"
              rel="noopener noreferrer"
              className="text-muted-foreground hover:text-primary transition-all duration-300 hover:scale-125"
              aria-label={social.label}
            >
              <social.icon size={24} />
            </a>
          ))}
        </motion.div>
      </div>
    </footer>
  );
}
