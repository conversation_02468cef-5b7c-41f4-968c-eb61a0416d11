"use client";

import <PERSON><PERSON><PERSON><PERSON> from "@/components/hero-section";
import NavBar from "@/components/navigation";
import { ReactLenis } from "lenis/react";
import AboutSection from "@/components/about-section";
import StackSection from "@/components/stack-section";
import ProjectsSection from "@/components/projects-section";
import TestimonialsSection from "@/components/testimonials-section";
import ContactSection from "@/components/contact-section";
import Footer from "@/components/footer";
export default function Home() {
  return (
    <ReactLenis root>
      <NavBar />
      <HeroSection />
      <AboutSection />
      <ProjectsSection />
      <TestimonialsSection />
      {/* <StackSection /> */}
      <ContactSection />
      <Footer />
    </ReactLenis>
  );
}
