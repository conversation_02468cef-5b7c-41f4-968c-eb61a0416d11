@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

@utility container {
  margin-inline: auto;
  padding-inline: 2rem;
  @media (width >= --theme(--breakpoint-sm)) {
    max-width: none;
  }
  @media (width >= 1400px) {
    max-width: 1400px;
  }
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.1408 0.0044 285.8229);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.1408 0.0044 285.8229);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.1408 0.0044 285.8229);
  --primary: oklch(0.6316 0.2507 18.2379);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.9512 0.0034 325.6036);
  --secondary-foreground: oklch(0.1408 0.0044 285.8229);
  --muted: oklch(0.9674 0.0013 286.3752);
  --muted-foreground: oklch(0.5517 0.0138 285.9385);
  --accent: oklch(0.9566 0.0216 359.6451);
  --accent-foreground: oklch(0.1408 0.0044 285.8229);
  --destructive: oklch(0.583 0.2097 28.8443);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.8895 0.0054 286.289);
  --input: oklch(0.9674 0.0013 286.3752);
  --ring: oklch(0.6318 0.2499 15.1424);
  --chart-1: oklch(0.7151 0.1785 27.9687);
  --chart-2: oklch(0.5115 0.0914 295.6879);
  --chart-3: oklch(0.7063 0.1375 127.6345);
  --chart-4: oklch(0.8777 0.0512 19.6691);
  --chart-5: oklch(0.7293 0.0647 262.745);
  --sidebar: oklch(0.9846 0.0017 247.8389);
  --sidebar-foreground: oklch(0.1408 0.0044 285.8229);
  --sidebar-primary: oklch(0.6318 0.2499 15.1424);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.8711 0.0055 286.286);
  --sidebar-accent-foreground: oklch(0.1408 0.0044 285.8229);
  --sidebar-border: oklch(0.8711 0.0055 286.286);
  --sidebar-ring: oklch(0.6318 0.2499 15.1424);
  --radius: 0.75rem;
  --shadow-2xs: 0px 8px 12px -1px hsl(0 0% 14.1176% / 0.05);
  --shadow-xs: 0px 8px 12px -1px hsl(0 0% 14.1176% / 0.05);
  --shadow-sm: 0px 8px 12px -1px hsl(0 0% 14.1176% / 0.1),
    0px 1px 2px -2px hsl(0 0% 14.1176% / 0.1);
  --shadow: 0px 8px 12px -1px hsl(0 0% 14.1176% / 0.08),
    0px 1px 2px -2px hsl(0 0% 14.1176% / 0.09);
  --shadow-md: 0px 8px 12px -1px hsl(0 0% 14.1176% / 0.1),
    0px 2px 4px -2px hsl(0 0% 14.1176% / 0.1);
  --shadow-lg: 0px 8px 12px -1px hsl(0 0% 14.1176% / 0.1),
    0px 4px 6px -2px hsl(0 0% 14.1176% / 0.1);
  --shadow-xl: 0px 8px 12px -1px hsl(0 0% 14.1176% / 0.1),
    0px 8px 10px -2px hsl(0 0% 14.1176% / 0.1);
  --shadow-2xl: 0px 8px 12px -1px hsl(0 0% 14.1176% / 0.25);
  --tracking-normal: 0.025em;
  --spacing: 0.25rem;
}

.dark {
  --background: oklch(0.1667 0.0039 345.7458);
  --foreground: oklch(0.9288 0.0126 255.5078);
  --card: oklch(0.162 0.0039 345.7672);
  --card-foreground: oklch(0.9288 0.0126 255.5078);
  --popover: oklch(0.2376 0.0114 285.4997);
  --popover-foreground: oklch(0.9288 0.0126 255.5078);
  --primary: oklch(62.93% 0.24981 21);
  --primary-foreground: oklch(0.9829 0.0089 0.1222);
  --secondary: oklch(0.2838 0.0102 332.5289);
  --secondary-foreground: oklch(0.9288 0.0126 255.5078);
  --muted: oklch(0.2739 0.0055 286.0326);
  --muted-foreground: oklch(0.7118 0.0129 286.0665);
  --accent: oklch(0.2039 0.0157 356.7148);
  --accent-foreground: oklch(0.9487 0.0271 354.5138);
  --destructive: oklch(0.6368 0.2078 25.3313);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.2603 0 0);
  --input: oklch(0.2376 0.0114 285.4997);
  --ring: oklch(0.6318 0.2499 15.1424);
  --chart-1: oklch(0.7151 0.1785 27.9687);
  --chart-2: oklch(0.5115 0.0914 295.6879);
  --chart-3: oklch(0.7063 0.1375 127.6345);
  --chart-4: oklch(0.8777 0.0512 19.6691);
  --chart-5: oklch(0.7293 0.0647 262.745);
  --sidebar: oklch(0.1845 0.0101 285.3646);
  --sidebar-foreground: oklch(0.9288 0.0126 255.5078);
  --sidebar-primary: oklch(0.6318 0.2499 15.1424);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.3703 0.0119 285.8054);
  --sidebar-accent-foreground: oklch(0.9288 0.0126 255.5078);
  --sidebar-border: oklch(0.3703 0.0119 285.8054);
  --sidebar-ring: oklch(0.6318 0.2499 15.1424);
  --radius: 0.75rem;
  --shadow-2xs: 0px 8px 12px -1px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 8px 12px -1px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 8px 12px -1px hsl(0 0% 0% / 0.1),
    0px 1px 2px -2px hsl(0 0% 0% / 0.1);
  --shadow: 0px 8px 12px -1px hsl(0 0% 0% / 0.1),
    0px 1px 2px -2px hsl(0 0% 0% / 0.1);
  --shadow-md: 0px 8px 12px -1px hsl(0 0% 0% / 0.1),
    0px 2px 4px -2px hsl(0 0% 0% / 0.1);
  --shadow-lg: 0px 8px 12px -1px hsl(0 0% 0% / 0.1),
    0px 4px 6px -2px hsl(0 0% 0% / 0.1);
  --shadow-xl: 0px 8px 12px -1px hsl(0 0% 0% / 0.1),
    0px 8px 10px -2px hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0px 8px 12px -1px hsl(0 0% 0% / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);

  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-normal: var(--tracking-normal);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  html {
    scroll-behavior: smooth;
  }
  body {
    @apply bg-background text-foreground selection:bg-primary selection:text-primary-foreground;
    letter-spacing: var(--tracking-normal);
  }
  ::-webkit-scrollbar {
    @apply w-1.5 h-1;
  }
  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }
  ::-webkit-scrollbar-thumb {
    @apply bg-primary/60 rounded-2xl;
  }
}

.txt-glow-primary {
  text-shadow: 0 0 14px hsla(347, 98%, 52%, 0.6);
}

.txt-glow {
  text-shadow: 0 0 14px hsla(349, 100%, 97%, 0.5);
}

.marquee-text {
  animation: marquee 10s linear infinite;
}
.marquee-text-2 {
  animation: marquee 7s linear infinite reverse;
}
@keyframes marquee {
  100% {
    transform: translateX(-50%);
  }
}
.marquee-text > p,
.marquee-text-2 > p {
  @apply text-foreground/80 md:text-muted-foreground/80 mb-0;
}
.masked {
  mask-image: linear-gradient(
    to right,
    transparent,
    black 30%,
    black 70%,
    transparent
  );
}

.blurIn {
  animation: blur-in ease-in-out;
  animation-timeline: view(50% -100px);
}
@keyframes blur-in {
  0% {
    transform: translateY(100px) translateX(-50%) rotate(-20deg);
    opacity: 0;
    filter: hue-rotate(0deg);
    filter: blur(10px);
  }
  100% {
    transform: translateY(0px) translateX(-50%) rotate(12deg);
    opacity: 0.8;
    filter: hue-rotate(-30deg);
    filter: blur(0px);
  }
}

.text-big {
  font-size: clamp(7rem, 15vw, 16rem);
}

.shadowinset {
  box-shadow: inset -2px -8px 20px rgba(138, 16, 36, 0.25);
}
