import { Button } from "@/components/ui/button";
import { instrumentserif } from "@/lib/font";
import { cn } from "@/lib/utils";
import Link from "next/link";

const page = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-primary/20 to-black flex items-center justify-center px-4">
      <div className="max-w-2xl mx-auto text-center">
        <div className="mb-8">
          <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-r from-primary rounded-full flex items-center justify-center">
            <svg
              className="w-12 h-12 text-primary-foreground"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
          <h1
            className={cn(
              "text-4xl md:text-6xl txt-glow tracking-tight text-foreground mb-4",
              instrumentserif.className
            )}
          >
            ✦ Thank You ✦
          </h1>
          <p className="text-lg text-muted-foreground mb-8">
            Your message has been received successfully. I appreciate you taking
            the time to reach out, and I&apos;ll get back to you as soon as
            possible.
          </p>
        </div>

        <div className="space-y-6">
          <div className="bg-card/50 backdrop-blur-lg rounded-lg p-6 border border-border">
            <h2 className="text-2xl text-foreground mb-4 tracking-tight">
              What&apos;s Next?
            </h2>
            <div className="grid md:grid-cols-2 gap-4 text-left">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-muted-foreground">
                  I&apos;ll review your message within 24 hours
                </p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-muted-foreground">
                  You&apos;ll receive a personalized response
                </p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-muted-foreground">
                  Let&apos;s discuss your project in detail
                </p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-muted-foreground">
                  We&apos;ll explore possibilities together
                </p>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/">
              <Button>Back to Home</Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default page;
