import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { ThemeProvider } from "next-themes";
import ThemeTogglebutton from "@/components/ui/theme-toggle-button";
import { instrumentsans } from "@/lib/font";

export const metadata: Metadata = {
  title: "<PERSON><PERSON>'s Portfolio",
  description: "Hi! Im <PERSON>. Welcome to my portoflio :)",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={instrumentsans.className}>
        <ThemeProvider attribute="class">
          <ThemeTogglebutton
            classname="top-2 right-6 z-60 hidden fixed sm:flex"
            start="top-right"
          />
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
