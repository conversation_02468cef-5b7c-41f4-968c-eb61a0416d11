import carti from "@/assets/designs/carti.png";
import Flavours from "@/assets/designs/seasons.png";
import seasons from "@/assets/designs/seasons.png";
import DarkGreyMinimalistMusicConcertPoster from "@/assets/designs/Dark Grey Minimalist Music Concert Poster.png";
import ferrari from "@/assets/designs/FFRRARI.png";
import headphone from "@/assets/designs/HEADPHONE.png";
import shoptoKuthi from "@/assets/designs/Shopto Kuthi.png";
import woman from "@/assets/designs/woman.png";
import YellowIllustrativeWeAreHiringPoster from "@/assets/designs/Yellow Illustrative We Are Hiring Poster.png";
import yumm from "@/assets/designs/YUMM!.png";
import wereHiring from "@/assets/designs/WE’RE HIRING!.png";
import onePiece from "@/assets/designs/onepiece.png";

export const testimonials = [
  {
    id: 1,
    name: "Xeven",
    role: "Founder @EdgeX",
    content: "",
    rating: 5,
    image: `https://xvatar.vercel.app/api/avatar/xeven`,
  },
  {
    id: 2,
    name: "",
    role: "Lead Designer, Creative Agency",
    content:
      "As a fellow UI/UX designer, I'm truly impressed by <PERSON>inia's ability to create visually stunning and user-friendly interfaces. Their development skills bring designs to life effortlessly. A true professional in the field!",
    rating: 5,
    image: `https://xvatar.vercel.app/api/avatar/michael`,
  },
  {
    id: 3,
    name: "",
    role: "Founder, Startup Dynamics",
    content:
      "We collaborated with Zinia on a critical project, and their UI/UX designs not only met but exceeded our expectations. It's a pleasure to work with professionals who understand the art and science of user experience.",
    rating: 5,
    image: `https://xvatar.vercel.app/api/avatar/michael`,
  },
  {
    id: 4,
    name: "David Thompson",
    role: "CTO, Digital Solutions",
    content:
      "Zinia's technical expertise and creative vision make them an invaluable partner. Their ability to translate complex requirements into elegant solutions is remarkable. Highly recommended for any development project.",
    rating: 5,
    image: `https://xvatar.vercel.app/api/avatar/michael`,
  },
];

export const projects = [
  {
    title: "Carti Music Poster",
    image: carti,
    tags: ["Poster", "Music", "Brutalist"],
  },
  {
    title: "YUMM! Bakery",
    image: yumm,
    tags: ["Branding", "Packaging", "Design"],
  },
  {
    title: "Woman Illustration",
    image: woman,
    tags: ["Illustration", "Editorial", "Digital Art"],
  },
  {
    title: "Shopto Kuthi Poster",
    image: shoptoKuthi,
    tags: ["Poster", "Promotion", "Design"],
  },
  {
    title: "Ferrari Poster",
    image: ferrari,
    tags: ["Poster", "Automotive", "Design"],
  },
  {
    title: "Headphone Product Shot",
    image: headphone,
    tags: ["Product", "Photography", "Editing"],
  },
  {
    title: "Music Concert Poster",
    image: DarkGreyMinimalistMusicConcertPoster,
    tags: ["Poster", "Music", "Minimalist"],
  },
  {
    title: "One Piece Slideshow",
    image: onePiece,
    tags: ["Slideshow", "Anime", "Fun"],
  },
  {
    title: "IceCream Flavours",
    image: Flavours,
    tags: ["Slideshow", "Promotion"],
  },
  {
    title: "We're Hiring Poster",
    image: wereHiring,
    tags: ["Poster", "Agency", "Hiring"],
  },
  {
    title: "Seasonal Showcase",
    image: seasons,
    tags: ["Slideshow", "Showcase"],
  },
  {
    title: "My First Poster",
    image: YellowIllustrativeWeAreHiringPoster,
    tags: ["Poster", "Agency", "Hiring"],
  },
];
