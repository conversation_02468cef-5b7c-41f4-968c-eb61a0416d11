# Next.js Project with ShadCn

This project demonstrates a basic setup of a Next.js application integrated with ShadCn UI and comes with theme toggle for dark and light modes.

## Features ✨

- **NextJS 15 app router**: Build your UI with the latest version of NextJS 14.
- **Tailwind CSS v4**: Style your application with utility-first CSS framework Tailwind CSS.
- **Shadcn UI** : The most trending UI library for modern looking apps. Input, button, sheet, sonner , dropdown and avatar already added!
- **Theme Toggle**: Includes functionality for switching between dark and light themes, improving user experience and accessibility.


### Prerequisites

Ensure you have Node.js installed on your machine. You can check by running `node -v` in your terminal. If not, download and install it from [Node.js website](https://nodejs.org).

### Installation

1. **You can either use this as a template or git clone it:**

   ```sh
   git clone --depth 1 https://github.com/Xeven777/next-shadcn-template.git

   ```

This will create a new directory named `next-shadcn-template` in your current directory.

2. **Remove the old `.git` directory and initialize a new one:**
   Navigate to the new `next-shadcn-template` directory and run the following commands:

   ```sh
   cd next-shadcn-template
   rm -rf .git
   git init
   ```

3. **Install the dependencies:**
   Run the following command to install the project dependencies:

   ```sh
   npm/bun install
   ```

4. **Start the development server:**
   After installing the dependencies, start the development server by running:

   ```sh
   npm/bun run dev
   ```

   Now, you should be able to see the application running at `localhost:3000` in your web browser! ✨

##### You can also download the code as Zip or use it as a template.

### Contributing

We welcome contributions from the community. Whether you want to add new features, fix bugs, or improve documentation, your help is appreciated.

### Owner

The primary contact for inquiries about the project is [Anish Biswas](https://github.com/Xeven777). Feel free to reach out for any questions, suggestions, or issues related to the project. My [Linkedin](https://www.linkedin.com/in/anishbiswas777/).
